:root {
  /* Color Tokens - Based on Reference */
  --primary-bg: #0f0f0f;
  --secondary-bg: #1a1a1a;
  --panel-bg: #2a2a2a;
  --accent-yellow: #ffd700;
  --accent-yellow-dim: #ffcc00;
  --text-primary: #ffffff;
  --text-secondary: #aaaaaa;
  --text-dim: #888888;
  --border-primary: #444444;
  --border-accent: #666666;
  --glow-yellow: rgba(255, 215, 0, 0.3);
  --glow-yellow-strong: rgba(255, 215, 0, 0.6);

  /* Legacy colors for compatibility */
  --error-red: #ff4444;
  --warning-yellow: #ffaa00;
  --info-blue: #4488ff;
  --success-green: #44ff44;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata', monospace;
  background: var(--primary-bg);
  color: var(--text-primary);
  overflow: hidden;
  height: 100vh;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.01) 0%, transparent 50%);
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

/* Header - Advanced Geometric Design */
.header {
  background: linear-gradient(90deg, var(--secondary-bg) 0%, var(--panel-bg) 100%);
  border-bottom: 1px solid var(--border-primary);
  padding: 0.75rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  position: relative;
  clip-path: polygon(
    0 0,
    calc(100% - 30px) 0,
    100% 30px,
    100% 100%,
    30px 100%,
    0 calc(100% - 30px)
  );
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(255, 215, 0, 0.05) 50%, transparent 100%);
  animation: header-scan 6s linear infinite;
}

.header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, var(--accent-yellow), transparent);
  opacity: 0.6;
  animation: pulse-line 2s ease-in-out infinite;
}

@keyframes header-scan {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes pulse-line {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.8; }
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo-text {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-yellow);
  text-shadow: 0 0 8px var(--glow-yellow);
  letter-spacing: 1px;
  position: relative;
}

.logo-accent {
  width: 30px;
  height: 15px;
  background: var(--accent-yellow);
  clip-path: polygon(0 0, 70% 0, 100% 50%, 70% 100%, 0 100%, 30% 50%);
  opacity: 0.8;
  animation: logo-pulse 3s ease-in-out infinite;
}

@keyframes logo-pulse {
  0%, 100% { opacity: 0.6; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.1); }
}

.status-bar {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.status-indicator {
  position: relative;
  width: 12px;
  height: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.indicator-core {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--accent-yellow);
  box-shadow: 0 0 6px var(--glow-yellow);
  animation: core-pulse 2s infinite;
}

.indicator-ring {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 1px solid var(--accent-yellow);
  border-radius: 50%;
  opacity: 0.5;
  animation: ring-expand 2s infinite;
}

.status-badge {
  padding: 0.2rem 0.6rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 8px, 100% 100%, 8px 100%, 0 calc(100% - 8px));
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.time-display {
  position: relative;
  padding: 0.2rem 0.6rem;
  background: var(--panel-bg);
  border: 1px solid var(--accent-yellow-dim);
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.8rem;
  color: var(--accent-yellow);
}

.time-accent {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: var(--accent-yellow);
  clip-path: polygon(0 0, 100% 0, 100% 100%);
  opacity: 0.7;
}

@keyframes core-pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

@keyframes ring-expand {
  0% { transform: scale(1); opacity: 0.5; }
  100% { transform: scale(1.5); opacity: 0; }
}

/* Main Content */
.main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 0.75rem;
  padding: 0.75rem;
  overflow: hidden;
}

.left-panels {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.panel-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
  height: 100%;
}

/* Panel System - Advanced Geometric Design with Border Fix */
.panel {
  background: var(--panel-bg);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  /* Remove border and clip-path from main element */
}

/* Create border using pseudo-element that follows the clip-path */
.panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--border-primary);
  clip-path: polygon(
    0 15px,
    15px 0,
    calc(100% - 15px) 0,
    100% 15px,
    100% calc(100% - 15px),
    calc(100% - 15px) 100%,
    15px 100%,
    0 calc(100% - 15px)
  );
  z-index: 0;
  transition: background 0.3s ease;
}

/* Inner content with same clip-path but smaller to create border effect */
.panel::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  right: 1px;
  bottom: 1px;
  background: var(--panel-bg);
  clip-path: polygon(
    0 14px,
    14px 0,
    calc(100% - 14px) 0,
    100% 14px,
    100% calc(100% - 14px),
    calc(100% - 14px) 100%,
    14px 100%,
    0 calc(100% - 14px)
  );
  z-index: 1;
}

.panel:hover::before {
  background: var(--accent-yellow-dim);
  box-shadow: 0 0 20px var(--glow-yellow);
}

.panel:hover {
  transform: translateY(-2px);
}

/* Fancy Rotating Border Animation */
.panel.animated-border {
  overflow: visible;
}

.panel.animated-border .border-animation {
  content: "";
  display: block;
  background: linear-gradient(
    90deg,
    rgba(255, 215, 0, 0) 0%,
    rgba(255, 215, 0, 0.75) 50%,
    rgba(255, 215, 0, 0) 100%
  );
  height: 300px;
  width: 100px;
  position: absolute;
  animation: border-rotate 5s linear infinite;
  z-index: 0;
  top: 50%;
  left: 50%;
  transform-origin: center center;
  pointer-events: none;
  clip-path: polygon(
    0 15px,
    15px 0,
    calc(100% - 15px) 0,
    100% 15px,
    100% calc(100% - 15px),
    calc(100% - 15px) 100%,
    15px 100%,
    0 calc(100% - 15px)
  );
}

@keyframes border-rotate {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
    opacity: 0;
  }
}

.panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 215, 0, 0.03) 50%, transparent 100%);
  pointer-events: none;
  animation: shimmer 4s ease-in-out infinite;
}

/* Geometric corner accents */
.panel::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 30px;
  height: 30px;
  background: linear-gradient(-45deg, var(--accent-yellow) 0%, transparent 70%);
  opacity: 0.15;
  clip-path: polygon(0 0, 100% 0, 100% 100%);
  animation: corner-glow 3s ease-in-out infinite alternate;
}

@keyframes shimmer {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes corner-glow {
  0% { opacity: 0.1; }
  100% { opacity: 0.3; }
}

/* Terminal - Futuristic Design */
.terminal-container {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  position: relative;
  clip-path: polygon(
    0 10px,
    10px 0,
    calc(100% - 10px) 0,
    100% 10px,
    100% calc(100% - 10px),
    calc(100% - 10px) 100%,
    10px 100%,
    0 calc(100% - 10px)
  );
}

.terminal-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 49%, var(--accent-yellow) 50%, transparent 51%),
    linear-gradient(-45deg, transparent 49%, var(--accent-yellow) 50%, transparent 51%);
  background-size: 20px 20px;
  opacity: 0.02;
  animation: grid-move 10s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(20px, 20px); }
}

.terminal-header {
  background: linear-gradient(90deg, var(--panel-bg), var(--secondary-bg));
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.terminal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-yellow-dim), transparent);
  opacity: 0.2;
}

.terminal-title {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0.5px;
}

.terminal-controls {
  display: flex;
  gap: 0.4rem;
}

.control-btn {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.control-btn:hover {
  opacity: 1;
}

.control-btn.close { background: #ff5f56; }
.control-btn.minimize { background: #ffbd2e; }
.control-btn.maximize { background: #27ca3f; }

.terminal-body {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  font-size: 0.9rem;
  line-height: 1.4;
}

.terminal-output {
  margin-bottom: 1rem;
  white-space: pre-wrap;
}

.terminal-line {
  margin-bottom: 0.5rem;
}

.prompt {
  color: var(--accent-yellow);
  font-weight: 600;
}

.command {
  color: var(--text-primary);
}

.error {
  color: var(--error-red);
}

.success {
  color: var(--success-green);
}

.warning {
  color: var(--warning-yellow);
}

.info {
  color: var(--info-blue);
}

.terminal-input-line {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terminal-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-family: inherit;
  font-size: inherit;
  outline: none;
}

.terminal-input::placeholder {
  color: var(--text-dim);
  opacity: 0.6;
}

.cursor {
  display: inline-block;
  width: 8px;
  height: 1.2em;
  background: var(--accent-yellow);
  animation: blink 1s infinite;
  box-shadow: 0 0 5px var(--glow-yellow);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Panel Headers */
.panel-header {
  background: linear-gradient(90deg, var(--panel-bg), var(--secondary-bg));
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.panel-title {
  color: var(--accent-yellow);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.panel-controls {
  display: flex;
  align-items: center;
  gap: 0.3rem;
}

.control-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--text-dim);
  opacity: 0.4;
  transition: all 0.3s ease;
}

.control-dot.active {
  background: var(--accent-yellow);
  opacity: 1;
  box-shadow: 0 0 4px var(--glow-yellow);
}

.panel-content {
  padding: 1rem;
  height: calc(100% - 40px);
  overflow: hidden;
  position: relative;
  z-index: 2;
}

.panel-header {
  position: relative;
  z-index: 2;
}

/* Network Visualization - Enhanced */
.network-viz {
  min-height: 300px;
  position: relative;
}

.network-viz::before {
  content: '';
  position: absolute;
  top: 10px;
  right: 10px;
  width: 40px;
  height: 20px;
  background: var(--accent-yellow);
  clip-path: polygon(0 0, 80% 0, 100% 50%, 80% 100%, 0 100%, 20% 50%);
  opacity: 0.3;
  animation: data-flow 2s ease-in-out infinite;
}

.network-graph {
  position: relative;
  width: 100%;
  height: 250px;
  background:
    radial-gradient(circle at center, rgba(255, 215, 0, 0.05) 0%, transparent 70%),
    linear-gradient(45deg, transparent 49%, rgba(255, 215, 0, 0.02) 50%, transparent 51%);
  background-size: 100% 100%, 30px 30px;
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  overflow: hidden;
  clip-path: polygon(
    0 8px,
    8px 0,
    calc(100% - 8px) 0,
    100% 8px,
    100% calc(100% - 8px),
    calc(100% - 8px) 100%,
    8px 100%,
    0 calc(100% - 8px)
  );
}

.network-graph::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(255, 215, 0, 0.03) 50%, transparent 100%);
  animation: network-scan 8s linear infinite;
}

@keyframes data-flow {
  0%, 100% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 0.6; transform: scale(1.1); }
}

@keyframes network-scan {
  0% { transform: translateX(-100%) translateY(-100%); }
  100% { transform: translateX(100%) translateY(100%); }
}

.network-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.network-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(255, 215, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 215, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
  animation: grid-drift 20s linear infinite;
}

@keyframes grid-drift {
  0% { transform: translate(0, 0); }
  100% { transform: translate(20px, 20px); }
}

.network-node {
  position: absolute;
  width: 50px;
  height: 50px;
  background: var(--panel-bg);
  border: 2px solid var(--accent-yellow);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px var(--glow-yellow);
  overflow: hidden;
}

.node-inner {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 0.6rem;
  font-weight: 600;
  color: var(--accent-yellow);
  line-height: 1;
  text-align: center;
  z-index: 2;
}

.network-node:hover {
  transform: translate(-50%, -50%) scale(1.15);
  box-shadow: 0 0 25px var(--glow-yellow-strong);
}

.network-node.central {
  width: 60px;
  height: 60px;
  background: radial-gradient(circle, var(--accent-yellow), var(--accent-yellow-dim));
  color: var(--primary-bg);
  box-shadow: 0 0 30px var(--glow-yellow-strong);
  border-width: 3px;
}

.network-node.central .node-inner {
  color: var(--primary-bg);
  font-weight: 700;
}

.node-orbit {
  position: absolute;
  width: 80px;
  height: 80px;
  border: 1px solid var(--accent-yellow);
  border-radius: 50%;
  opacity: 0.3;
  animation: orbit-rotate 10s linear infinite;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes orbit-rotate {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.network-node.compromised {
  border-color: var(--error-red);
  box-shadow: 0 0 15px rgba(255, 68, 68, 0.7);
  animation: breach-alert 2s ease-in-out infinite;
}

.network-node.compromised .node-inner {
  color: var(--error-red);
}

.breach-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 15px;
  height: 15px;
  background: var(--error-red);
  border-radius: 50%;
  animation: breach-pulse 1s ease-in-out infinite;
}

@keyframes breach-alert {
  0%, 100% { border-color: var(--error-red); }
  50% { border-color: #ff8888; }
}

@keyframes breach-pulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.3); opacity: 0.7; }
}

.node-pulse {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--accent-yellow);
  border-radius: 50%;
  animation: pulse-ring 2s infinite;
  opacity: 0;
}

.network-node.compromised .node-pulse {
  border-color: var(--error-red);
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}

/* Data Packets */
.data-packet {
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--accent-yellow);
  border-radius: 50%;
  box-shadow: 0 0 8px var(--glow-yellow);
  animation: packet-float 4s ease-in-out infinite;
  z-index: 3;
}

.data-packet.breach {
  background: var(--error-red);
  box-shadow: 0 0 8px rgba(255, 68, 68, 0.7);
  animation: packet-breach 2s ease-in-out infinite;
}

@keyframes packet-float {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.8;
  }
  25% {
    transform: translate(10px, -5px) scale(1.2);
    opacity: 1;
  }
  50% {
    transform: translate(-5px, 10px) scale(0.8);
    opacity: 0.6;
  }
  75% {
    transform: translate(-10px, -8px) scale(1.1);
    opacity: 0.9;
  }
}

@keyframes packet-breach {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(15px, -15px) scale(1.5);
    opacity: 0.7;
  }
}

/* Enhanced Network Connections */
.network-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.data-flow {
  filter: drop-shadow(0 0 3px var(--glow-yellow));
}

.breach-flow {
  filter: drop-shadow(0 0 5px rgba(255, 68, 68, 0.7));
}

/* Sidebar */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* System Stats */
.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
}

.stat-label {
  color: var(--text-dim);
}

.stat-value {
  color: var(--primary-green);
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--bg-dark);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.25rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), var(--secondary-green));
  transition: width 0.3s ease;
  box-shadow: 0 0 5px var(--primary-green);
}

/* Network Activity */
.network-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 255, 65, 0.1);
  font-size: 0.8rem;
}

.network-item:last-child {
  border-bottom: none;
}

.network-status {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.network-status.active {
  background: rgba(0, 255, 65, 0.2);
  color: var(--primary-green);
}

.network-status.scanning {
  background: rgba(255, 255, 0, 0.2);
  color: var(--warning-yellow);
}

.network-status.compromised {
  background: rgba(255, 0, 64, 0.2);
  color: var(--error-red);
}

/* Tools */
.tool-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.tool-btn {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  color: var(--text-secondary);
  padding: 0.6rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 0.75rem;
  text-align: center;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.tool-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--glow-yellow), transparent);
  transition: left 0.5s ease;
}

.tool-btn:hover {
  background: var(--panel-bg);
  color: var(--accent-yellow);
  border-color: var(--accent-yellow);
  box-shadow: 0 0 10px var(--glow-yellow);
  transform: translateY(-1px);
}

.tool-btn:hover::before {
  left: 100%;
}

.tool-btn.quantum {
  border-color: var(--accent-yellow-dim);
  color: var(--accent-yellow-dim);
}

.tool-btn:active {
  transform: translateY(0);
}

/* Target List */
.target-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.target-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.target-item:hover {
  border-color: var(--accent-yellow-dim);
  box-shadow: 0 0 5px var(--glow-yellow);
}

.target-info {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.target-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-primary);
}

.target-ip {
  font-size: 0.7rem;
  color: var(--text-dim);
  font-family: monospace;
}

.target-status {
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
  text-align: center;
  min-width: 70px;
}

.target-status.active {
  background: rgba(68, 255, 68, 0.2);
  color: var(--success-green);
  border: 1px solid var(--success-green);
}

.target-status.scanning {
  background: rgba(255, 170, 0, 0.2);
  color: var(--warning-yellow);
  border: 1px solid var(--warning-yellow);
}

.target-status.compromised {
  background: rgba(255, 68, 68, 0.2);
  color: var(--error-red);
  border: 1px solid var(--error-red);
}

/* Data Stream */
.data-stream {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stream-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: var(--secondary-bg);
  border-left: 3px solid var(--accent-yellow);
  border-radius: 0 4px 4px 0;
}

.stream-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.stream-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--accent-yellow);
  font-family: monospace;
}

.stream-value.warning {
  color: var(--warning-yellow);
}

.stream-value.success {
  color: var(--success-green);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--border-green);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-green);
}

/* Geometric Decorations */
.geometric-decorations {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.geo-element {
  position: absolute;
  background: var(--accent-yellow);
  opacity: 0.1;
  animation: float 6s ease-in-out infinite;
}

.geo-1 {
  top: 10%;
  left: 5%;
  width: 40px;
  height: 40px;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  animation-delay: 0s;
}

.geo-2 {
  top: 20%;
  right: 10%;
  width: 30px;
  height: 30px;
  clip-path: polygon(0 0, 80% 0, 100% 50%, 80% 100%, 0 100%, 20% 50%);
  animation-delay: 1s;
}

.geo-3 {
  bottom: 15%;
  left: 8%;
  width: 35px;
  height: 35px;
  clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
  animation-delay: 2s;
}

.geo-4 {
  bottom: 25%;
  right: 15%;
  width: 25px;
  height: 25px;
  clip-path: polygon(0 20%, 20% 0, 80% 0, 100% 20%, 100% 80%, 80% 100%, 20% 100%, 0% 80%);
  animation-delay: 3s;
}

@keyframes float {
  0%, 100% { transform: translateY(0) rotate(0deg); opacity: 0.05; }
  25% { transform: translateY(-10px) rotate(90deg); opacity: 0.15; }
  50% { transform: translateY(-5px) rotate(180deg); opacity: 0.1; }
  75% { transform: translateY(-15px) rotate(270deg); opacity: 0.2; }
}

/* Enhanced Progress Bars */
.progress-bar {
  width: 100%;
  height: 6px;
  background: var(--secondary-bg);
  border-radius: 0;
  overflow: hidden;
  margin-top: 0.25rem;
  position: relative;
  clip-path: polygon(0 0, calc(100% - 4px) 0, 100% 4px, 100% 100%, 4px 100%, 0 calc(100% - 4px));
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--accent-yellow-dim), var(--accent-yellow));
  transition: width 0.3s ease;
  box-shadow: 0 0 8px var(--glow-yellow);
  position: relative;
  overflow: hidden;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Ripple Effect Animation */
@keyframes ripple-expand {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Advanced Anchor Point Animations */
.anchor-system {
  position: relative;
}

/* Global anchor indicator */
.anchor-system::before {
  content: "";
  position: absolute;
  position-anchor: --active-element;
  border-radius: 50%;
  inset: auto calc(anchor(center) - 0.5em) anchor(bottom);
  aspect-ratio: 1;
  background: radial-gradient(circle, var(--accent-yellow), transparent);
  transition: 0.5s 0.3s, 0.5s 0.3s cubic-bezier(0, -2, 1, -2) bottom;
  z-index: 10;
  pointer-events: none;
  opacity: 0.8;
  box-shadow: 0 0 20px var(--glow-yellow-strong);
}

/* Anchor-enabled elements */
.anchor-element {
  position: relative;
  transition: all 0.3s ease;
}

.anchor-element::before {
  content: "";
  position: absolute;
  z-index: -1;
  inset: 100% 50% 0;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--accent-yellow-dim), var(--accent-yellow));
  transition: 0.4s;
  opacity: 0;
}

.anchor-element:hover::before,
.anchor-element.active::before {
  anchor-name: --active-element;
  inset: -5px;
  border-radius: 8px;
  opacity: 0.3;
  transition: 0.4s 0.2s;
}

/* Cross-DOM connection lines */
.connection-line {
  position: fixed;
  pointer-events: none;
  z-index: 5;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.connection-line.active {
  opacity: 1;
}

.connection-line::before {
  content: "";
  position: absolute;
  width: 2px;
  height: 100%;
  background: linear-gradient(
    to bottom,
    transparent,
    var(--accent-yellow),
    transparent
  );
  animation: connection-pulse 2s ease-in-out infinite;
}

@keyframes connection-pulse {
  0%, 100% {
    transform: scaleY(0.5);
    opacity: 0.5;
  }
  50% {
    transform: scaleY(1);
    opacity: 1;
  }
}

/* Enhanced Tool Buttons with Anchor Support */
.tool-btn.anchor-element:hover {
  anchor-name: --tool-active;
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 5px 15px var(--glow-yellow);
}

/* Network Nodes with Anchor Support */
.network-node.anchor-element:hover {
  anchor-name: --node-active;
}

/* Status Items with Anchor Support */
.status-item.anchor-element:hover {
  anchor-name: --status-active;
}

/* Responsive */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .sidebar {
    flex-direction: row;
    overflow-x: auto;
  }

  .panel {
    min-width: 250px;
  }

  .geometric-decorations {
    display: none;
  }

  /* Disable anchor animations on mobile for performance */
  .anchor-system::before {
    display: none;
  }
}
