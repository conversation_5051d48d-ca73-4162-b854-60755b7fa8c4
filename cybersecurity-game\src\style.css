:root {
  --primary-green: #00ff41;
  --secondary-green: #00cc33;
  --dark-green: #003d0f;
  --bg-black: #0a0a0a;
  --bg-dark: #1a1a1a;
  --text-green: #00ff41;
  --text-dim: #66ff66;
  --border-green: #00cc33;
  --error-red: #ff0040;
  --warning-yellow: #ffff00;
  --info-blue: #00ccff;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Fira Code', 'Courier New', monospace;
  background: var(--bg-black);
  color: var(--text-green);
  overflow: hidden;
  height: 100vh;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(0, 255, 65, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--bg-black) 0%, #0d1b0d 100%);
}

/* Header */
.header {
  background: linear-gradient(90deg, var(--bg-dark) 0%, var(--dark-green) 100%);
  border-bottom: 2px solid var(--border-green);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 10px rgba(0, 255, 65, 0.3);
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary-green);
  text-shadow: 0 0 10px var(--primary-green);
}

.status-bar {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-green);
  box-shadow: 0 0 5px var(--primary-green);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Main Content */
.main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 1rem;
  padding: 1rem;
  overflow: hidden;
}

/* Terminal */
.terminal-container {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--border-green);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 0 20px rgba(0, 255, 65, 0.2);
}

.terminal-header {
  background: var(--dark-green);
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border-green);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.terminal-title {
  font-size: 0.9rem;
  color: var(--text-green);
}

.terminal-controls {
  display: flex;
  gap: 0.5rem;
}

.control-btn {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
}

.control-btn.close { background: var(--error-red); }
.control-btn.minimize { background: var(--warning-yellow); }
.control-btn.maximize { background: var(--primary-green); }

.terminal-body {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  font-size: 0.9rem;
  line-height: 1.4;
}

.terminal-output {
  margin-bottom: 1rem;
  white-space: pre-wrap;
}

.terminal-line {
  margin-bottom: 0.5rem;
}

.prompt {
  color: var(--primary-green);
}

.command {
  color: var(--text-dim);
}

.error {
  color: var(--error-red);
}

.success {
  color: var(--primary-green);
}

.warning {
  color: var(--warning-yellow);
}

.info {
  color: var(--info-blue);
}

.terminal-input-line {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terminal-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-green);
  font-family: inherit;
  font-size: inherit;
  outline: none;
}

.cursor {
  display: inline-block;
  width: 8px;
  height: 1.2em;
  background: var(--primary-green);
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Sidebar */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.panel {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid var(--border-green);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 0 15px rgba(0, 255, 65, 0.1);
}

.panel-header {
  background: var(--dark-green);
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--border-green);
  font-weight: 600;
  font-size: 0.9rem;
}

.panel-content {
  padding: 1rem;
}

/* System Stats */
.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
}

.stat-label {
  color: var(--text-dim);
}

.stat-value {
  color: var(--primary-green);
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--bg-dark);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.25rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), var(--secondary-green));
  transition: width 0.3s ease;
  box-shadow: 0 0 5px var(--primary-green);
}

/* Network Activity */
.network-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 255, 65, 0.1);
  font-size: 0.8rem;
}

.network-item:last-child {
  border-bottom: none;
}

.network-status {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.network-status.active {
  background: rgba(0, 255, 65, 0.2);
  color: var(--primary-green);
}

.network-status.scanning {
  background: rgba(255, 255, 0, 0.2);
  color: var(--warning-yellow);
}

.network-status.compromised {
  background: rgba(255, 0, 64, 0.2);
  color: var(--error-red);
}

/* Tools */
.tool-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.tool-btn {
  background: var(--bg-dark);
  border: 1px solid var(--border-green);
  color: var(--text-green);
  padding: 0.75rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 0.8rem;
  text-align: center;
}

.tool-btn:hover {
  background: var(--dark-green);
  box-shadow: 0 0 10px rgba(0, 255, 65, 0.3);
  transform: translateY(-1px);
}

.tool-btn:active {
  transform: translateY(0);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--border-green);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-green);
}

/* Responsive */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .sidebar {
    flex-direction: row;
    overflow-x: auto;
  }

  .panel {
    min-width: 250px;
  }
}
