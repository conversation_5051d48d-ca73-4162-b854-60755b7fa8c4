:root {
  /* Color Tokens - Based on Reference */
  --primary-bg: #0f0f0f;
  --secondary-bg: #1a1a1a;
  --panel-bg: #2a2a2a;
  --accent-yellow: #ffd700;
  --accent-yellow-dim: #ffcc00;
  --text-primary: #ffffff;
  --text-secondary: #aaaaaa;
  --text-dim: #888888;
  --border-primary: #444444;
  --border-accent: #666666;
  --glow-yellow: rgba(255, 215, 0, 0.3);
  --glow-yellow-strong: rgba(255, 215, 0, 0.6);

  /* Legacy colors for compatibility */
  --error-red: #ff4444;
  --warning-yellow: #ffaa00;
  --info-blue: #4488ff;
  --success-green: #44ff44;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata', monospace;
  background: var(--primary-bg);
  color: var(--text-primary);
  overflow: hidden;
  height: 100vh;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(255, 215, 0, 0.02) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 215, 0, 0.01) 0%, transparent 50%);
}

#app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--primary-bg) 0%, var(--secondary-bg) 100%);
}

/* Header */
.header {
  background: linear-gradient(90deg, var(--secondary-bg) 0%, var(--panel-bg) 100%);
  border-bottom: 1px solid var(--border-primary);
  padding: 0.75rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  position: relative;
}

.header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-yellow), transparent);
  opacity: 0.3;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-yellow);
  text-shadow: 0 0 8px var(--glow-yellow);
  letter-spacing: 0.5px;
}

.status-bar {
  display: flex;
  gap: 2rem;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--accent-yellow);
  box-shadow: 0 0 4px var(--glow-yellow);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

/* Main Content */
.main-content {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 0.75rem;
  padding: 0.75rem;
  overflow: hidden;
}

.left-panels {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.panel-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.75rem;
  height: 100%;
}

/* Panel System - Geometric Design */
.panel {
  background: var(--panel-bg);
  border: 1px solid var(--border-primary);
  position: relative;
  overflow: hidden;
}

.panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(255, 215, 0, 0.02) 50%, transparent 100%);
  pointer-events: none;
}

/* Geometric corners */
.panel::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: linear-gradient(-45deg, var(--accent-yellow) 0%, transparent 70%);
  opacity: 0.1;
}

/* Terminal */
.terminal-container {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  position: relative;
}

.terminal-header {
  background: linear-gradient(90deg, var(--panel-bg), var(--secondary-bg));
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.terminal-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-yellow-dim), transparent);
  opacity: 0.2;
}

.terminal-title {
  font-size: 0.8rem;
  color: var(--text-secondary);
  font-weight: 500;
  letter-spacing: 0.5px;
}

.terminal-controls {
  display: flex;
  gap: 0.4rem;
}

.control-btn {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.control-btn:hover {
  opacity: 1;
}

.control-btn.close { background: #ff5f56; }
.control-btn.minimize { background: #ffbd2e; }
.control-btn.maximize { background: #27ca3f; }

.terminal-body {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  font-size: 0.9rem;
  line-height: 1.4;
}

.terminal-output {
  margin-bottom: 1rem;
  white-space: pre-wrap;
}

.terminal-line {
  margin-bottom: 0.5rem;
}

.prompt {
  color: var(--accent-yellow);
  font-weight: 600;
}

.command {
  color: var(--text-primary);
}

.error {
  color: var(--error-red);
}

.success {
  color: var(--success-green);
}

.warning {
  color: var(--warning-yellow);
}

.info {
  color: var(--info-blue);
}

.terminal-input-line {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.terminal-input {
  flex: 1;
  background: transparent;
  border: none;
  color: var(--text-primary);
  font-family: inherit;
  font-size: inherit;
  outline: none;
}

.terminal-input::placeholder {
  color: var(--text-dim);
  opacity: 0.6;
}

.cursor {
  display: inline-block;
  width: 8px;
  height: 1.2em;
  background: var(--accent-yellow);
  animation: blink 1s infinite;
  box-shadow: 0 0 5px var(--glow-yellow);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Panel Headers */
.panel-header {
  background: linear-gradient(90deg, var(--panel-bg), var(--secondary-bg));
  padding: 0.5rem 1rem;
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.panel-title {
  color: var(--accent-yellow);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.panel-controls {
  color: var(--text-dim);
  font-size: 0.7rem;
}

.panel-content {
  padding: 1rem;
  height: calc(100% - 40px);
  overflow: hidden;
}

/* Network Visualization */
.network-viz {
  min-height: 300px;
}

.network-graph {
  position: relative;
  width: 100%;
  height: 250px;
  background: radial-gradient(circle at center, rgba(255, 215, 0, 0.05) 0%, transparent 70%);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  overflow: hidden;
}

.network-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.network-node {
  position: absolute;
  width: 40px;
  height: 40px;
  background: var(--panel-bg);
  border: 2px solid var(--accent-yellow);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
  color: var(--accent-yellow);
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px var(--glow-yellow);
}

.network-node:hover {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 0 15px var(--glow-yellow-strong);
}

.network-node.central {
  width: 50px;
  height: 50px;
  background: var(--accent-yellow);
  color: var(--primary-bg);
  box-shadow: 0 0 20px var(--glow-yellow-strong);
}

.network-node.compromised {
  border-color: var(--error-red);
  color: var(--error-red);
  box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
}

.node-pulse {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid var(--accent-yellow);
  border-radius: 50%;
  animation: pulse-ring 2s infinite;
  opacity: 0;
}

.network-node.compromised .node-pulse {
  border-color: var(--error-red);
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* Sidebar */
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* System Stats */
.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
}

.stat-label {
  color: var(--text-dim);
}

.stat-value {
  color: var(--primary-green);
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--bg-dark);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 0.25rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-green), var(--secondary-green));
  transition: width 0.3s ease;
  box-shadow: 0 0 5px var(--primary-green);
}

/* Network Activity */
.network-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid rgba(0, 255, 65, 0.1);
  font-size: 0.8rem;
}

.network-item:last-child {
  border-bottom: none;
}

.network-status {
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 500;
}

.network-status.active {
  background: rgba(0, 255, 65, 0.2);
  color: var(--primary-green);
}

.network-status.scanning {
  background: rgba(255, 255, 0, 0.2);
  color: var(--warning-yellow);
}

.network-status.compromised {
  background: rgba(255, 0, 64, 0.2);
  color: var(--error-red);
}

/* Tools */
.tool-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.tool-btn {
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  color: var(--text-secondary);
  padding: 0.6rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  font-size: 0.75rem;
  text-align: center;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.tool-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--glow-yellow), transparent);
  transition: left 0.5s ease;
}

.tool-btn:hover {
  background: var(--panel-bg);
  color: var(--accent-yellow);
  border-color: var(--accent-yellow);
  box-shadow: 0 0 10px var(--glow-yellow);
  transform: translateY(-1px);
}

.tool-btn:hover::before {
  left: 100%;
}

.tool-btn.quantum {
  border-color: var(--accent-yellow-dim);
  color: var(--accent-yellow-dim);
}

.tool-btn:active {
  transform: translateY(0);
}

/* Target List */
.target-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.target-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: var(--secondary-bg);
  border: 1px solid var(--border-primary);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.target-item:hover {
  border-color: var(--accent-yellow-dim);
  box-shadow: 0 0 5px var(--glow-yellow);
}

.target-info {
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.target-name {
  font-size: 0.8rem;
  font-weight: 600;
  color: var(--text-primary);
}

.target-ip {
  font-size: 0.7rem;
  color: var(--text-dim);
  font-family: monospace;
}

.target-status {
  padding: 0.2rem 0.5rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
  text-align: center;
  min-width: 70px;
}

.target-status.active {
  background: rgba(68, 255, 68, 0.2);
  color: var(--success-green);
  border: 1px solid var(--success-green);
}

.target-status.scanning {
  background: rgba(255, 170, 0, 0.2);
  color: var(--warning-yellow);
  border: 1px solid var(--warning-yellow);
}

.target-status.compromised {
  background: rgba(255, 68, 68, 0.2);
  color: var(--error-red);
  border: 1px solid var(--error-red);
}

/* Data Stream */
.data-stream {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stream-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: var(--secondary-bg);
  border-left: 3px solid var(--accent-yellow);
  border-radius: 0 4px 4px 0;
}

.stream-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.stream-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--accent-yellow);
  font-family: monospace;
}

.stream-value.warning {
  color: var(--warning-yellow);
}

.stream-value.success {
  color: var(--success-green);
}

/* Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--border-green);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-green);
}

/* Responsive */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .sidebar {
    flex-direction: row;
    overflow-x: auto;
  }

  .panel {
    min-width: 250px;
  }
}
