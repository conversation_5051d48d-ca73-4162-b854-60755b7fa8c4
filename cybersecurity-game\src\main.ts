import './style.css'

interface SystemStats {
  cpu: number;
  memory: number;
  network: number;
  security: number;
}

interface NetworkTarget {
  ip: string;
  name: string;
  status: 'active' | 'scanning' | 'compromised';
  ports: number[];
}

class CyberSecGame {
  private terminalOutput: HTMLElement;
  private terminalInput: HTMLInputElement;
  private systemStats: SystemStats;
  private networkTargets: NetworkTarget[];
  private commandHistory: string[];
  private historyIndex: number;

  constructor() {
    this.systemStats = {
      cpu: 45,
      memory: 67,
      network: 23,
      security: 89
    };

    this.networkTargets = [
      { ip: '***********', name: 'Router', status: 'active', ports: [22, 80, 443] },
      { ip: '***********00', name: 'Server-01', status: 'scanning', ports: [21, 22, 80, 443, 3389] },
      { ip: '********', name: 'Database', status: 'compromised', ports: [1433, 3306, 5432] },
      { ip: '***********', name: 'Workstation', status: 'active', ports: [135, 139, 445] }
    ];

    this.commandHistory = [];
    this.historyIndex = -1;

    this.init();
  }

  private init() {
    this.createUI();
    this.setupEventListeners();
    this.startSystemUpdates();
    this.showWelcomeMessage();
  }

  private createUI() {
    const app = document.querySelector<HTMLDivElement>('#app')!;

    app.innerHTML = `
      <div class="header">
        <div class="logo-section">
          <div class="logo">CyberSec Terminal</div>
          <div class="status-item">
            <div class="status-indicator"></div>
            <span>SYSTEM ONLINE</span>
          </div>
        </div>
        <div class="status-bar">
          <div class="status-item">
            <span>User: root@hackbox</span>
          </div>
          <div class="status-item">
            <span id="current-time">${new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>

      <div class="main-content">
        <div class="terminal-container">
          <div class="terminal-header">
            <div class="terminal-title">Terminal - /home/<USER>/div>
            <div class="terminal-controls">
              <button class="control-btn close"></button>
              <button class="control-btn minimize"></button>
              <button class="control-btn maximize"></button>
            </div>
          </div>
          <div class="terminal-body">
            <div id="terminal-output" class="terminal-output"></div>
            <div class="terminal-input-line">
              <span class="prompt">root@hackbox:~$</span>
              <input type="text" id="terminal-input" class="terminal-input" autocomplete="off" spellcheck="false">
              <span class="cursor"></span>
            </div>
          </div>
        </div>

        <div class="sidebar">
          <div class="panel">
            <div class="panel-header">System Stats</div>
            <div class="panel-content">
              <div class="stat-item">
                <span class="stat-label">CPU Usage</span>
                <span class="stat-value" id="cpu-value">${this.systemStats.cpu}%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" id="cpu-progress" style="width: ${this.systemStats.cpu}%"></div>
              </div>

              <div class="stat-item">
                <span class="stat-label">Memory</span>
                <span class="stat-value" id="memory-value">${this.systemStats.memory}%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" id="memory-progress" style="width: ${this.systemStats.memory}%"></div>
              </div>

              <div class="stat-item">
                <span class="stat-label">Network</span>
                <span class="stat-value" id="network-value">${this.systemStats.network}%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" id="network-progress" style="width: ${this.systemStats.network}%"></div>
              </div>

              <div class="stat-item">
                <span class="stat-label">Security</span>
                <span class="stat-value" id="security-value">${this.systemStats.security}%</span>
              </div>
              <div class="progress-bar">
                <div class="progress-fill" id="security-progress" style="width: ${this.systemStats.security}%"></div>
              </div>
            </div>
          </div>

          <div class="panel">
            <div class="panel-header">Network Targets</div>
            <div class="panel-content" id="network-targets">
              ${this.renderNetworkTargets()}
            </div>
          </div>

          <div class="panel">
            <div class="panel-header">Hacking Tools</div>
            <div class="panel-content">
              <div class="tool-grid">
                <button class="tool-btn" data-tool="nmap">Nmap</button>
                <button class="tool-btn" data-tool="metasploit">Metasploit</button>
                <button class="tool-btn" data-tool="wireshark">Wireshark</button>
                <button class="tool-btn" data-tool="burpsuite">Burp Suite</button>
                <button class="tool-btn" data-tool="sqlmap">SQLMap</button>
                <button class="tool-btn" data-tool="hydra">Hydra</button>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;

    this.terminalOutput = document.getElementById('terminal-output')!;
    this.terminalInput = document.getElementById('terminal-input') as HTMLInputElement;
  }

  private renderNetworkTargets(): string {
    return this.networkTargets.map(target => `
      <div class="network-item">
        <div>
          <div style="font-weight: 500;">${target.name}</div>
          <div style="font-size: 0.7rem; color: var(--text-dim);">${target.ip}</div>
        </div>
        <div class="network-status ${target.status}">${target.status.toUpperCase()}</div>
      </div>
    `).join('');
  }

  private setupEventListeners() {
    this.terminalInput.addEventListener('keydown', (e) => {
      if (e.key === 'Enter') {
        this.handleCommand();
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        this.navigateHistory(-1);
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        this.navigateHistory(1);
      }
    });

    // Tool buttons
    document.querySelectorAll('.tool-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        const tool = (e.target as HTMLElement).dataset.tool;
        if (tool) {
          this.executeTool(tool);
        }
      });
    });

    // Update time
    setInterval(() => {
      const timeElement = document.getElementById('current-time');
      if (timeElement) {
        timeElement.textContent = new Date().toLocaleTimeString();
      }
    }, 1000);
  }

  private navigateHistory(direction: number) {
    if (this.commandHistory.length === 0) return;

    this.historyIndex += direction;

    if (this.historyIndex < 0) {
      this.historyIndex = -1;
      this.terminalInput.value = '';
    } else if (this.historyIndex >= this.commandHistory.length) {
      this.historyIndex = this.commandHistory.length - 1;
    }

    if (this.historyIndex >= 0) {
      this.terminalInput.value = this.commandHistory[this.historyIndex];
    }
  }

  private handleCommand() {
    const command = this.terminalInput.value.trim();
    if (!command) return;

    this.commandHistory.unshift(command);
    this.historyIndex = -1;

    this.addToOutput(`<span class="prompt">root@hackbox:~$</span> <span class="command">${command}</span>`);

    this.executeCommand(command);
    this.terminalInput.value = '';
  }

  private executeCommand(command: string) {
    const [cmd, ...args] = command.toLowerCase().split(' ');

    switch (cmd) {
      case 'help':
        this.showHelp();
        break;
      case 'ls':
        this.listFiles();
        break;
      case 'nmap':
        this.runNmap(args);
        break;
      case 'exploit':
        this.runExploit(args);
        break;
      case 'scan':
        this.runScan(args);
        break;
      case 'crack':
        this.runCrack(args);
        break;
      case 'clear':
        this.clearTerminal();
        break;
      case 'whoami':
        this.addToOutput('<span class="success">root</span>');
        break;
      case 'pwd':
        this.addToOutput('<span class="info">/home/<USER>/span>');
        break;
      case 'date':
        this.addToOutput(`<span class="info">${new Date().toString()}</span>`);
        break;
      case 'ps':
        this.showProcesses();
        break;
      case 'netstat':
        this.showNetstat();
        break;
      default:
        this.addToOutput(`<span class="error">Command not found: ${cmd}</span>`);
        this.addToOutput('<span class="info">Type "help" for available commands</span>');
    }
  }

  private showHelp() {
    const helpText = `
<span class="info">Available Commands:</span>
<span class="success">help</span>        - Show this help message
<span class="success">ls</span>          - List files and directories
<span class="success">nmap [ip]</span>   - Scan network target
<span class="success">exploit [ip]</span> - Attempt to exploit target
<span class="success">scan [type]</span> - Run vulnerability scan
<span class="success">crack [hash]</span> - Crack password hash
<span class="success">clear</span>       - Clear terminal
<span class="success">whoami</span>      - Show current user
<span class="success">pwd</span>         - Show current directory
<span class="success">date</span>        - Show current date/time
<span class="success">ps</span>          - Show running processes
<span class="success">netstat</span>     - Show network connections

<span class="warning">Use the tool buttons on the right for quick access to hacking tools.</span>
    `;
    this.addToOutput(helpText);
  }

  private listFiles() {
    const files = `
<span class="info">drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 11 00:33 <span class="success">exploits</span></span>
<span class="info">drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 11 00:33 <span class="success">payloads</span></span>
<span class="info">drwxr-xr-x  2 <USER> <GROUP>  4096 Dec 11 00:33 <span class="success">tools</span></span>
<span class="info">-rw-r--r--  1 <USER> <GROUP>  1337 Dec 11 00:33 <span class="command">targets.txt</span></span>
<span class="info">-rw-r--r--  1 <USER> <GROUP>  2048 Dec 11 00:33 <span class="command">wordlist.txt</span></span>
<span class="info">-rwxr-xr-x  1 <USER> <GROUP>  8192 Dec 11 00:33 <span class="warning">backdoor.sh</span></span>
    `;
    this.addToOutput(files);
  }
