import './style.css'

// Simple UI setup without complex logic
function createCyberSecUI() {
  const app = document.querySelector<HTMLDivElement>('#app')!;

  app.innerHTML = `
    <div class="anchor-system">
      <div class="header">
        <div class="logo-section">
          <div class="logo anchor-element">
            <span class="logo-text">BLACK GLASSMORPHISM</span>
            <div class="logo-accent"></div>
          </div>
          <div class="status-item anchor-element">
            <div class="status-indicator">
              <div class="indicator-core"></div>
              <div class="indicator-ring"></div>
            </div>
            <span>QUANTUM ONLINE</span>
          </div>
        </div>
        <div class="status-bar">
          <div class="status-item anchor-element">
            <div class="status-badge">
              <span>root@quantum-core</span>
            </div>
          </div>
          <div class="status-item anchor-element">
            <div class="time-display">
              <span id="current-time">${new Date().toLocaleTimeString()}</span>
              <div class="time-accent"></div>
            </div>
          </div>
        </div>
      </div>

    <!-- Geometric Decorations -->
    <div class="geometric-decorations">
      <div class="geo-element geo-1"></div>
      <div class="geo-element geo-2"></div>
      <div class="geo-element geo-3"></div>
      <div class="geo-element geo-4"></div>
    </div>

    <div class="main-content">
      <div class="left-panels">
        <div class="panel-grid">
          <!-- Main Terminal Panel -->
          <div class="panel terminal-container animated-border">
            <div class="border-animation"></div>
            <div class="terminal-header">
              <div class="terminal-title">Black Glassmorphism Terminal</div>
              <div class="terminal-controls">
                <button class="control-btn close"></button>
                <button class="control-btn minimize"></button>
                <button class="control-btn maximize"></button>
              </div>
            </div>
            <div class="terminal-body">
              <div class="terminal-output">
                <div class="terminal-line"><span class="success">Welcome to Black Glassmorphism v3.1.0</span></div>
                <div class="terminal-line"><span class="info">Quantum encryption protocols initialized</span></div>
                <div class="terminal-line"><span class="warning">Neural network security active</span></div>
                <div class="terminal-line"><span class="prompt">root@quantum:~$</span> <span class="command">scan --deep --neural 192.168.0.0/16</span></div>
                <div class="terminal-line"><span class="success">Deep neural scan initiated...</span></div>
                <div class="terminal-line"><span class="info">Quantum entanglement detected: 192.168.1.1</span></div>
                <div class="terminal-line"><span class="info">NEURAL_PORT  STATE    QUANTUM_SERVICE</span></div>
                <div class="terminal-line"><span class="info">22/tcp       open     quantum-ssh</span></div>
                <div class="terminal-line"><span class="info">443/tcp      secure   neural-https</span></div>
                <div class="terminal-line"><span class="info">8080/tcp     filtered glassmorphic-api</span></div>
                <div class="terminal-line"><span class="prompt">root@quantum:~$</span> <span class="command">exploit --quantum --target 192.168.1.100</span></div>
                <div class="terminal-line"><span class="warning">Quantum exploitation in progress...</span></div>
                <div class="terminal-line"><span class="success">Neural vulnerability detected: QVE-2024-9999</span></div>
                <div class="terminal-line"><span class="success">Glassmorphic shell access established!</span></div>
                <div class="terminal-line"><span class="prompt">root@quantum:~$</span> <span class="cursor"></span></div>
              </div>
              <div class="terminal-input-line">
                <span class="prompt">root@quantum:~$</span>
                <input type="text" class="terminal-input" placeholder="Enter quantum commands..." autocomplete="off" spellcheck="false">
                <span class="cursor"></span>
              </div>
            </div>
          </div>

          <!-- Network Visualization Panel -->
          <div class="panel network-viz">
            <div class="panel-header">
              <div class="panel-title">Quantum Network Topology</div>
              <div class="panel-controls">
                <span class="control-dot active"></span>
                <span class="control-dot"></span>
                <span class="control-dot"></span>
              </div>
            </div>
            <div class="panel-content">
              <div class="network-graph">
                <!-- Animated Grid Background -->
                <div class="network-grid"></div>

                <!-- Central Core Node -->
                <div class="network-node central anchor-element" style="top: 50%; left: 50%;">
                  <div class="node-pulse"></div>
                  <div class="node-inner">
                    <span>QUANTUM</span>
                    <span>CORE</span>
                  </div>
                  <div class="node-orbit"></div>
                </div>

                <!-- Database Node -->
                <div class="network-node anchor-element" style="top: 20%; left: 30%;">
                  <div class="node-pulse"></div>
                  <div class="node-inner">
                    <span>NEURAL</span>
                    <span>DB</span>
                  </div>
                </div>

                <!-- Web Server Node -->
                <div class="network-node anchor-element" style="top: 80%; left: 70%;">
                  <div class="node-pulse"></div>
                  <div class="node-inner">
                    <span>GLASS</span>
                    <span>WEB</span>
                  </div>
                </div>

                <!-- Compromised Server -->
                <div class="network-node compromised anchor-element" style="top: 30%; left: 80%;">
                  <div class="node-pulse"></div>
                  <div class="node-inner">
                    <span>MORPH</span>
                    <span>SRV</span>
                  </div>
                  <div class="breach-indicator"></div>
                </div>

                <!-- API Gateway -->
                <div class="network-node anchor-element" style="top: 70%; left: 20%;">
                  <div class="node-pulse"></div>
                  <div class="node-inner">
                    <span>API</span>
                    <span>GTW</span>
                  </div>
                </div>

                <!-- Animated Connections -->
                <svg class="network-connections">
                  <defs>
                    <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" style="stop-color:var(--accent-yellow);stop-opacity:0" />
                      <stop offset="50%" style="stop-color:var(--accent-yellow);stop-opacity:1" />
                      <stop offset="100%" style="stop-color:var(--accent-yellow);stop-opacity:0" />
                    </linearGradient>
                    <linearGradient id="breachGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" style="stop-color:var(--error-red);stop-opacity:0" />
                      <stop offset="50%" style="stop-color:var(--error-red);stop-opacity:1" />
                      <stop offset="100%" style="stop-color:var(--error-red);stop-opacity:0" />
                    </linearGradient>
                  </defs>

                  <!-- Data Flow Lines -->
                  <line x1="50%" y1="50%" x2="30%" y2="20%" stroke="url(#connectionGradient)" stroke-width="2" class="data-flow">
                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="3s" repeatCount="indefinite"/>
                  </line>
                  <line x1="50%" y1="50%" x2="70%" y2="80%" stroke="url(#connectionGradient)" stroke-width="2" class="data-flow">
                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="2.5s" repeatCount="indefinite"/>
                  </line>
                  <line x1="50%" y1="50%" x2="80%" y2="30%" stroke="url(#breachGradient)" stroke-width="3" class="breach-flow">
                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="1.5s" repeatCount="indefinite"/>
                  </line>
                  <line x1="50%" y1="50%" x2="20%" y2="70%" stroke="url(#connectionGradient)" stroke-width="2" class="data-flow">
                    <animate attributeName="stroke-dasharray" values="0,100;50,50;100,0" dur="2.8s" repeatCount="indefinite"/>
                  </line>
                </svg>

                <!-- Data Packets -->
                <div class="data-packet" style="top: 35%; left: 40%;"></div>
                <div class="data-packet" style="top: 65%; left: 60%;"></div>
                <div class="data-packet breach" style="top: 40%; left: 65%;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="sidebar">
        <div class="panel">
          <div class="panel-header">System Stats</div>
          <div class="panel-content">
            <div class="stat-item">
              <span class="stat-label">CPU Usage</span>
              <span class="stat-value">45%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 45%"></div>
            </div>

            <div class="stat-item">
              <span class="stat-label">Memory</span>
              <span class="stat-value">67%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 67%"></div>
            </div>

            <div class="stat-item">
              <span class="stat-label">Network</span>
              <span class="stat-value">23%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 23%"></div>
            </div>

            <div class="stat-item">
              <span class="stat-label">Security</span>
              <span class="stat-value">89%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 89%"></div>
            </div>
          </div>
        </div>

        <div class="panel">
          <div class="panel-header">Network Targets</div>
          <div class="panel-content">
            <div class="network-item">
              <div>
                <div style="font-weight: 500;">Router</div>
                <div style="font-size: 0.7rem; color: var(--text-dim);">192.168.1.1</div>
              </div>
              <div class="network-status active">ACTIVE</div>
            </div>
            <div class="network-item">
              <div>
                <div style="font-weight: 500;">Server-01</div>
                <div style="font-size: 0.7rem; color: var(--text-dim);">192.168.1.100</div>
              </div>
              <div class="network-status scanning">SCANNING</div>
            </div>
            <div class="network-item">
              <div>
                <div style="font-weight: 500;">Database</div>
                <div style="font-size: 0.7rem; color: var(--text-dim);">10.0.0.5</div>
              </div>
              <div class="network-status compromised">COMPROMISED</div>
            </div>
            <div class="network-item">
              <div>
                <div style="font-weight: 500;">Workstation</div>
                <div style="font-size: 0.7rem; color: var(--text-dim);">172.16.0.10</div>
              </div>
              <div class="network-status active">ACTIVE</div>
            </div>
          </div>
        </div>

        <div class="panel">
          <div class="panel-header">Hacking Tools</div>
          <div class="panel-content">
            <div class="tool-grid">
              <button class="tool-btn anchor-element">Q-Nmap</button>
              <button class="tool-btn anchor-element">NeuroSploit</button>
              <button class="tool-btn anchor-element">GlassShark</button>
              <button class="tool-btn anchor-element">MorphSuite</button>
              <button class="tool-btn anchor-element">QuantumMap</button>
              <button class="tool-btn anchor-element">NeuralHydra</button>
            </div>
          </div>
        </div>
      </div>
    </div>
    </div>
  `;

  // Simple time update
  setInterval(() => {
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
      timeElement.textContent = new Date().toLocaleTimeString();
    }
  }, 1000);

  // Enhanced anchor point interactions
  setupAnchorAnimations();
}

function setupAnchorAnimations() {
  const anchorElements = document.querySelectorAll('.anchor-element');

  anchorElements.forEach((element) => {
    element.addEventListener('mouseenter', () => {
      // Add active class for enhanced effects
      element.classList.add('active');

      // Create connection lines to other elements
      createConnectionLines(element, anchorElements);

      // Trigger ripple effect
      createRippleEffect(element);
    });

    element.addEventListener('mouseleave', () => {
      element.classList.remove('active');
      removeConnectionLines();
    });
  });
}

function createConnectionLines(activeElement: Element, allElements: NodeListOf<Element>) {
  // Remove existing lines
  removeConnectionLines();

  const activeRect = activeElement.getBoundingClientRect();

  allElements.forEach((element) => {
    if (element !== activeElement) {
      const targetRect = element.getBoundingClientRect();

      // Create connection line
      const line = document.createElement('div');
      line.className = 'connection-line active';

      // Calculate position and angle
      const deltaX = targetRect.left + targetRect.width / 2 - (activeRect.left + activeRect.width / 2);
      const deltaY = targetRect.top + targetRect.height / 2 - (activeRect.top + activeRect.height / 2);
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      const angle = Math.atan2(deltaY, deltaX) * 180 / Math.PI;

      // Position the line
      line.style.left = `${activeRect.left + activeRect.width / 2}px`;
      line.style.top = `${activeRect.top + activeRect.height / 2}px`;
      line.style.width = `${distance}px`;
      line.style.height = '2px';
      line.style.transform = `rotate(${angle}deg)`;
      line.style.transformOrigin = '0 50%';

      document.body.appendChild(line);
    }
  });
}

function removeConnectionLines() {
  const existingLines = document.querySelectorAll('.connection-line');
  existingLines.forEach(line => line.remove());
}

function createRippleEffect(element: Element) {
  const ripple = document.createElement('div');
  ripple.className = 'ripple-effect';
  ripple.style.cssText = `
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle, var(--accent-yellow), transparent);
    width: 20px;
    height: 20px;
    pointer-events: none;
    z-index: 1000;
    animation: ripple-expand 0.6s ease-out forwards;
  `;

  const rect = element.getBoundingClientRect();
  ripple.style.left = `${rect.left + rect.width / 2 - 10}px`;
  ripple.style.top = `${rect.top + rect.height / 2 - 10}px`;

  document.body.appendChild(ripple);

  setTimeout(() => ripple.remove(), 600);
}

// Initialize the UI when the page loads
createCyberSecUI();
