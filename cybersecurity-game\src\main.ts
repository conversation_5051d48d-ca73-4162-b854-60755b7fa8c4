import './style.css'

// Simple UI setup without complex logic
function createCyberSecUI() {
  const app = document.querySelector<HTMLDivElement>('#app')!;

  app.innerHTML = `
    <div class="header">
      <div class="logo-section">
        <div class="logo">CyberSec Terminal</div>
        <div class="status-item">
          <div class="status-indicator"></div>
          <span>SYSTEM ONLINE</span>
        </div>
      </div>
      <div class="status-bar">
        <div class="status-item">
          <span>User: root@hackbox</span>
        </div>
        <div class="status-item">
          <span id="current-time">${new Date().toLocaleTimeString()}</span>
        </div>
      </div>
    </div>

    <div class="main-content">
      <div class="left-panels">
        <div class="panel-grid">
          <!-- Main Terminal Panel -->
          <div class="panel terminal-container">
            <div class="terminal-header">
              <div class="terminal-title">Black Glassmorphism Terminal</div>
              <div class="terminal-controls">
                <button class="control-btn close"></button>
                <button class="control-btn minimize"></button>
                <button class="control-btn maximize"></button>
              </div>
            </div>
            <div class="terminal-body">
              <div class="terminal-output">
                <div class="terminal-line"><span class="success">Welcome to Black Glassmorphism v3.1.0</span></div>
                <div class="terminal-line"><span class="info">Quantum encryption protocols initialized</span></div>
                <div class="terminal-line"><span class="warning">Neural network security active</span></div>
                <div class="terminal-line"><span class="prompt">root@quantum:~$</span> <span class="command">scan --deep --neural 192.168.0.0/16</span></div>
                <div class="terminal-line"><span class="success">Deep neural scan initiated...</span></div>
                <div class="terminal-line"><span class="info">Quantum entanglement detected: 192.168.1.1</span></div>
                <div class="terminal-line"><span class="info">NEURAL_PORT  STATE    QUANTUM_SERVICE</span></div>
                <div class="terminal-line"><span class="info">22/tcp       open     quantum-ssh</span></div>
                <div class="terminal-line"><span class="info">443/tcp      secure   neural-https</span></div>
                <div class="terminal-line"><span class="info">8080/tcp     filtered glassmorphic-api</span></div>
                <div class="terminal-line"><span class="prompt">root@quantum:~$</span> <span class="command">exploit --quantum --target 192.168.1.100</span></div>
                <div class="terminal-line"><span class="warning">Quantum exploitation in progress...</span></div>
                <div class="terminal-line"><span class="success">Neural vulnerability detected: QVE-2024-9999</span></div>
                <div class="terminal-line"><span class="success">Glassmorphic shell access established!</span></div>
                <div class="terminal-line"><span class="prompt">root@quantum:~$</span> <span class="cursor"></span></div>
              </div>
              <div class="terminal-input-line">
                <span class="prompt">root@quantum:~$</span>
                <input type="text" class="terminal-input" placeholder="Enter quantum commands..." autocomplete="off" spellcheck="false">
                <span class="cursor"></span>
              </div>
            </div>
          </div>

          <!-- Network Visualization Panel -->
          <div class="panel network-viz">
            <div class="panel-header">
              <div class="panel-title">Network Topology</div>
              <div class="panel-controls">●●●</div>
            </div>
            <div class="panel-content">
              <div class="network-graph">
                <div class="network-node central" style="top: 50%; left: 50%;">
                  <div class="node-pulse"></div>
                  <span>CORE</span>
                </div>
                <div class="network-node" style="top: 20%; left: 30%;">
                  <div class="node-pulse"></div>
                  <span>DB</span>
                </div>
                <div class="network-node" style="top: 80%; left: 70%;">
                  <div class="node-pulse"></div>
                  <span>WEB</span>
                </div>
                <div class="network-node compromised" style="top: 30%; left: 80%;">
                  <div class="node-pulse"></div>
                  <span>SRV</span>
                </div>
                <svg class="network-connections">
                  <line x1="50%" y1="50%" x2="30%" y2="20%" stroke="var(--accent-yellow)" stroke-width="1" opacity="0.6"/>
                  <line x1="50%" y1="50%" x2="70%" y2="80%" stroke="var(--accent-yellow)" stroke-width="1" opacity="0.6"/>
                  <line x1="50%" y1="50%" x2="80%" y2="30%" stroke="var(--error-red)" stroke-width="2" opacity="0.8"/>
                </svg>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="sidebar">
        <div class="panel">
          <div class="panel-header">System Stats</div>
          <div class="panel-content">
            <div class="stat-item">
              <span class="stat-label">CPU Usage</span>
              <span class="stat-value">45%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 45%"></div>
            </div>

            <div class="stat-item">
              <span class="stat-label">Memory</span>
              <span class="stat-value">67%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 67%"></div>
            </div>

            <div class="stat-item">
              <span class="stat-label">Network</span>
              <span class="stat-value">23%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 23%"></div>
            </div>

            <div class="stat-item">
              <span class="stat-label">Security</span>
              <span class="stat-value">89%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" style="width: 89%"></div>
            </div>
          </div>
        </div>

        <div class="panel">
          <div class="panel-header">Network Targets</div>
          <div class="panel-content">
            <div class="network-item">
              <div>
                <div style="font-weight: 500;">Router</div>
                <div style="font-size: 0.7rem; color: var(--text-dim);">192.168.1.1</div>
              </div>
              <div class="network-status active">ACTIVE</div>
            </div>
            <div class="network-item">
              <div>
                <div style="font-weight: 500;">Server-01</div>
                <div style="font-size: 0.7rem; color: var(--text-dim);">192.168.1.100</div>
              </div>
              <div class="network-status scanning">SCANNING</div>
            </div>
            <div class="network-item">
              <div>
                <div style="font-weight: 500;">Database</div>
                <div style="font-size: 0.7rem; color: var(--text-dim);">10.0.0.5</div>
              </div>
              <div class="network-status compromised">COMPROMISED</div>
            </div>
            <div class="network-item">
              <div>
                <div style="font-weight: 500;">Workstation</div>
                <div style="font-size: 0.7rem; color: var(--text-dim);">172.16.0.10</div>
              </div>
              <div class="network-status active">ACTIVE</div>
            </div>
          </div>
        </div>

        <div class="panel">
          <div class="panel-header">Hacking Tools</div>
          <div class="panel-content">
            <div class="tool-grid">
              <button class="tool-btn">Nmap</button>
              <button class="tool-btn">Metasploit</button>
              <button class="tool-btn">Wireshark</button>
              <button class="tool-btn">Burp Suite</button>
              <button class="tool-btn">SQLMap</button>
              <button class="tool-btn">Hydra</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;

  // Simple time update
  setInterval(() => {
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
      timeElement.textContent = new Date().toLocaleTimeString();
    }
  }, 1000);
}

// Initialize the UI when the page loads
createCyberSecUI();
